[project]
name = "pyldz"
version = "0.1.0"
description = "Project to fetch data from Google Sheets and generate Hugo pages for pythonlodz.org"
readme = "README.md"
authors = [{ name = "<PERSON><PERSON><PERSON><PERSON>", email = "grz<PERSON><PERSON>@kocjan.me" }]
requires-python = ">=3.13"
dependencies = [
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "google-api-python-client>=2.174.0",
    "rich>=14.0.0",
    "google-auth>=2.40.3",
    "google-auth-oauthlib>=1.2.2",
    "typer>=0.16.0",
    "pillow>=11.3.0",
    "requests>=2.32.4",
    "unidecode>=1.4.0",
]

[dependency-groups]
dev = ["mypy>=1.16.1", "pytest>=8.4.1", "ruff>=0.12.1"]

[project.scripts]
pyldz = "pyldz.main:main"


[tool.pytest.ini_options]
testpaths = ["tests"]

addopts = ["-vvv"]


[tool.ruff]
line-length = 88
target-version = "py38"

[tool.ruff.lint]
select = ["E", "F", "W", "I", "N", "UP", "B", "A", "C4", "T20"]
ignore = ["E501"]                                               # Line too long (handled by formatter)

[tool.ruff.lint.isort]
known-first-party = ["pyldz"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
