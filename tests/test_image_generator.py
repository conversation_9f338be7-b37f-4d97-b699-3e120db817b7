#!/usr/bin/env python3
"""Tests for image generation functionality."""

import datetime
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from PIL import Image

from pyldz.image_generator import ImageGenerationError, MeetupImageGenerator
from pyldz.meetup import Language, Meetup, Speaker, Talk


@pytest.fixture
def temp_assets_dir(tmp_path):
    """Create a temporary assets directory with test files."""
    assets_dir = tmp_path / "assets"
    assets_dir.mkdir()

    # Create directories
    (assets_dir / "images").mkdir()
    (assets_dir / "images" / "avatars").mkdir()
    (assets_dir / "fonts").mkdir()

    # Create test template images
    template_solo = Image.new("RGBA", (1920, 1080), (255, 255, 255, 255))
    template_solo.save(assets_dir / "images" / "infographic_template.png")

    template_duo = Image.new("RGBA", (1920, 1080), (255, 255, 255, 255))
    template_duo.save(assets_dir / "images" / "infographic_template_duo.png")

    # Create test mask
    mask = Image.new("L", (300, 300), 255)
    mask.save(assets_dir / "images" / "avatars" / "mask.png")

    # Create TBA avatar
    tba = Image.new("RGBA", (300, 300), (128, 128, 128, 255))
    tba.save(assets_dir / "images" / "avatars" / "tba.png")

    # Create test font files (empty files for testing)
    (assets_dir / "fonts" / "OpenSans-Medium.ttf").touch()
    (assets_dir / "fonts" / "OpenSans-Bold.ttf").touch()

    return assets_dir


@pytest.fixture
def sample_meetup():
    """Create a sample meetup for testing."""
    return Meetup(
        meetup_id="42",
        title="Meetup #42",
        date=datetime.date(2024, 6, 27),
        time="18:00",
        location="Test Venue, Test Street 123",
        talks=[
            Talk(
                speaker_id="john-doe",
                title="Introduction to Clean Architecture",
                description="Learn clean architecture principles.",
                language=Language.EN,
                title_en="Introduction to Clean Architecture",
            )
        ],
        sponsors=["sponsor1"],
    )


@pytest.fixture
def sample_duo_meetup():
    """Create a sample meetup with two talks."""
    return Meetup(
        meetup_id="43",
        title="Meetup #43",
        date=datetime.date(2024, 7, 25),
        time="18:00",
        location="Test Venue, Test Street 123",
        talks=[
            Talk(
                speaker_id="john-doe",
                title="Introduction to Clean Architecture",
                description="Learn clean architecture principles.",
                language=Language.EN,
                title_en="Introduction to Clean Architecture",
            ),
            Talk(
                speaker_id="jane-smith",
                title="Advanced Python Patterns",
                description="Deep dive into Python patterns.",
                language=Language.PL,
                title_en="Advanced Python Patterns",
            ),
        ],
        sponsors=["sponsor1"],
    )


@pytest.fixture
def sample_speaker():
    """Create a sample speaker for testing."""
    return Speaker(
        id="john-doe",
        name="John Doe",
        bio="A developer",
        avatar_path="https://example.com/avatar.jpg",
        social_links=[],
    )


@pytest.fixture
def sample_speakers():
    """Create sample speakers for testing."""
    return [
        Speaker(
            id="john-doe",
            name="John Doe",
            bio="A developer",
            avatar_path="https://example.com/avatar1.jpg",
            social_links=[],
        ),
        Speaker(
            id="jane-smith",
            name="Jane Smith",
            bio="Another developer",
            avatar_path="https://example.com/avatar2.jpg",
            social_links=[],
        ),
    ]


class TestMeetupImageGenerator:
    """Test the MeetupImageGenerator class."""

    def test_init(self, temp_assets_dir, tmp_path):
        """Test generator initialization."""
        cache_dir = tmp_path / "cache"
        generator = MeetupImageGenerator(temp_assets_dir, cache_dir)

        assert generator.assets_dir == temp_assets_dir
        assert generator.cache_dir == cache_dir
        assert cache_dir.exists()

    def test_init_with_default_cache(self, temp_assets_dir):
        """Test generator initialization with default cache directory."""
        generator = MeetupImageGenerator(temp_assets_dir)

        expected_cache = temp_assets_dir.parent / "cache" / "avatars"
        assert generator.cache_dir == expected_cache
        assert expected_cache.exists()

    def test_generate_featured_image_no_talks(self, temp_assets_dir, tmp_path):
        """Test generating image for meetup with no talks."""
        generator = MeetupImageGenerator(temp_assets_dir)

        meetup = Meetup(
            meetup_id="44",
            title="Meetup #44",
            date=datetime.date(2024, 8, 29),
            time="18:00",
            location="Test Venue",
            talks=[],
            sponsors=[],
        )

        output_path = tmp_path / "featured.png"

        # Use the default font loading which will fall back to default font
        result = generator.generate_featured_image(meetup, [], output_path)

        assert result == output_path
        assert output_path.exists()

    def test_generate_featured_image_solo(
        self, temp_assets_dir, tmp_path, sample_meetup, sample_speaker
    ):
        """Test generating image for meetup with one talk."""
        generator = MeetupImageGenerator(temp_assets_dir)
        output_path = tmp_path / "featured.png"

        with patch.object(generator, "_get_speaker_avatar") as mock_get_avatar:
            mock_get_avatar.return_value = Image.new(
                "RGBA", (300, 300), (255, 0, 0, 255)
            )

            result = generator.generate_featured_image(
                sample_meetup, [sample_speaker], output_path
            )

        assert result == output_path
        assert output_path.exists()

    def test_generate_featured_image_duo(
        self, temp_assets_dir, tmp_path, sample_duo_meetup, sample_speakers
    ):
        """Test generating image for meetup with two talks."""
        generator = MeetupImageGenerator(temp_assets_dir)
        output_path = tmp_path / "featured.png"

        with patch.object(generator, "_get_speaker_avatar") as mock_get_avatar:
            mock_get_avatar.return_value = Image.new(
                "RGBA", (240, 240), (255, 0, 0, 255)
            )

            result = generator.generate_featured_image(
                sample_duo_meetup, sample_speakers, output_path
            )

        assert result == output_path
        assert output_path.exists()

    def test_generate_featured_image_missing_template(
        self, temp_assets_dir, tmp_path, sample_meetup
    ):
        """Test error handling when template is missing."""
        # Remove the template
        (temp_assets_dir / "images" / "infographic_template.png").unlink()

        generator = MeetupImageGenerator(temp_assets_dir)
        output_path = tmp_path / "featured.png"

        with pytest.raises(ImageGenerationError, match="Solo template not found"):
            generator.generate_featured_image(sample_meetup, [], output_path)

    def test_find_speaker_by_id(self, temp_assets_dir, sample_speakers):
        """Test finding speaker by ID."""
        generator = MeetupImageGenerator(temp_assets_dir)

        speaker = generator._find_speaker_by_id(sample_speakers, "john-doe")
        assert speaker is not None
        assert speaker.name == "John Doe"

        speaker = generator._find_speaker_by_id(sample_speakers, "nonexistent")
        assert speaker is None

    @patch("requests.get")
    def test_get_speaker_avatar_download(
        self, mock_get, temp_assets_dir, sample_speaker
    ):
        """Test downloading and caching speaker avatar."""
        generator = MeetupImageGenerator(temp_assets_dir)

        # Mock successful download
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None

        # Create a test image in memory
        test_image = Image.new("RGB", (100, 100), (255, 0, 0))
        from io import BytesIO

        img_bytes = BytesIO()
        test_image.save(img_bytes, format="PNG")
        img_bytes.seek(0)
        mock_response.content = img_bytes.getvalue()

        mock_get.return_value = mock_response

        avatar = generator._get_speaker_avatar(sample_speaker, (50, 50))

        assert avatar is not None
        assert avatar.size == (50, 50)

        # Check that avatar was cached
        cache_file = generator.cache_dir / f"{sample_speaker.id}.png"
        assert cache_file.exists()

    def test_get_speaker_avatar_from_cache(self, temp_assets_dir, sample_speaker):
        """Test loading speaker avatar from cache."""
        generator = MeetupImageGenerator(temp_assets_dir)

        # Create cached avatar
        cache_file = generator.cache_dir / f"{sample_speaker.id}.png"
        test_image = Image.new("RGBA", (100, 100), (0, 255, 0, 255))
        test_image.save(cache_file)

        avatar = generator._get_speaker_avatar(sample_speaker, (50, 50))

        assert avatar is not None
        assert avatar.size == (50, 50)

    @patch("requests.get")
    def test_get_speaker_avatar_download_error(
        self, mock_get, temp_assets_dir, sample_speaker
    ):
        """Test handling download errors for speaker avatar."""
        generator = MeetupImageGenerator(temp_assets_dir)

        # Mock failed download
        mock_get.side_effect = Exception("Network error")

        avatar = generator._get_speaker_avatar(sample_speaker, (50, 50))

        assert avatar is None

    def test_apply_circular_mask(self, temp_assets_dir):
        """Test applying circular mask to image."""
        generator = MeetupImageGenerator(temp_assets_dir)

        test_image = Image.new("RGBA", (100, 100), (255, 0, 0, 255))
        masked = generator._apply_circular_mask(test_image)

        assert masked.size == test_image.size
        assert masked.mode == "RGBA"

    def test_apply_circular_mask_missing_mask_file(self, temp_assets_dir):
        """Test applying circular mask when mask file is missing."""
        # Remove the mask file
        (temp_assets_dir / "images" / "avatars" / "mask.png").unlink()

        generator = MeetupImageGenerator(temp_assets_dir)

        test_image = Image.new("RGBA", (100, 100), (255, 0, 0, 255))
        masked = generator._apply_circular_mask(test_image)

        assert masked.size == test_image.size
        assert masked.mode == "RGBA"

    def test_create_circular_mask(self, temp_assets_dir):
        """Test creating circular mask."""
        generator = MeetupImageGenerator(temp_assets_dir)

        test_image = Image.new("RGBA", (100, 100), (255, 0, 0, 255))
        masked = generator._create_circular_mask(test_image)

        assert masked.size == test_image.size
        assert masked.mode == "RGBA"
