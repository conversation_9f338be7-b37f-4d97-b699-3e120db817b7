{{ $fontNormal := resources.Get "fonts/OpenSans-Medium.ttf" }}
{{ $fontBold := resources.Get "fonts/OpenSans-Bold.ttf" }}

{{ $meetup := .meetup }}

{{ $bg := resources.Get "images/infographic_template_duo.png" }}
{{ $final := $bg }}


{{ $imgWidth := $final.Width }}
{{ $date := (printf "%sr. godz. %s" ($meetup.date | time.Format "Monday 02.01.2006"| strings.ToUpper) $meetup.time) }}
{{ $mainDateOptions := dict 
  "color" "#393f5f" 
  "size" 80 
  "x" (div $imgWidth 2) 
  "y" 267.5
  "font" $fontNormal
  "alignx" "center" 
}}
{{ $final = $final.Filter (images.Text $date $mainDateOptions) }}

{{ $bottomDateOptions := dict 
  "color" "#393f5f" 
  "size" 32 
  "x" 157
  "y" 1010.3
  "font" $fontNormal
}}
{{ $final = $final.Filter (images.Text $date $bottomDateOptions) }}

{{ $talk1 := index $meetup.talks 0}}

{{ $speaker1 := index site.Data.speakers $talk1.speaker_id }}
{{ $author1Options := dict 
  "color" "#393f5f" 
  "size" 32 
  "x" 428.7
  "y" 543.8
  "font" $fontNormal
}}
{{ $final = $final.Filter (images.Text $speaker1.name $author1Options) }}

{{ $title1Options := dict 
      "color" "#393f5f" 
      "size" 32 
      "x" 428.7
      "y" 592.1 
      "font" $fontBold
  }}
{{ $final = $final.Filter (images.Text $talk1.talk_title $title1Options) }}

{{ $avatarMaskFilter := images.Mask (resources.Get "images/avatars/mask.png") }}

{{ $speaker1Img := resources.Get $speaker1.avatar }}
{{ $speaker1 := $speaker1Img.Fill "240x240 center" }}

{{ $maskedSpeaker1 := $speaker1  | images.Filter $avatarMaskFilter }}

{{ $final := $final.Filter (images.Overlay $maskedSpeaker1 122 490) }}

{{ $final := $final.Filter (images.Overlay $maskedSpeaker1 1537 585) }}
{{ $talk2 := index $meetup.talks 1}}

{{ $speaker2 := index site.Data.speakers $talk2.speaker_id }}
{{ $author1Options := dict 
  "alignx" "right" 
  "color" "#393f5f" 
  "size" 32 
  "x" 1495.8
  "y" 656.6
  "font" $fontNormal
}}
{{ $final = $final.Filter (images.Text $speaker2.name $author1Options) }}

{{ $title2Options := dict 
      "alignx" "right" 
      "color" "#393f5f" 
      "size" 32 
      "x" 1495.8
      "y" 704.9
      "font" $fontBold
  }}
{{ $final = $final.Filter (images.Text $talk2.talk_title $title2Options) }}

{{ $speaker2Img := resources.Get $speaker2.avatar }}
{{ $speaker2 := $speaker2Img.Fill "240x240 center" }}

{{ $maskedSpeaker2 := $speaker2 | images.Filter $avatarMaskFilter }}

{{ $final := $final.Filter (images.Overlay $maskedSpeaker2 1537 585) }}


{{ return $final }}
